import { useState } from "react";
import { useAuth } from "@/hooks/use-auth";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import Sidebar from "@/components/layout/sidebar";
import Header from "@/components/layout/header";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { CheckCircle, Calendar, Clock, User, AlertCircle } from "lucide-react";
import type { Appointment } from "@shared/schema";

export default function CheckIn() {
  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [sidebarOpen, setSidebarOpen] = useState(false);

  const { data: todayAppointments = [], isLoading } = useQuery<Appointment[]>({
    queryKey: ["/api/appointments/date", new Date().toISOString().split('T')[0]],
    enabled: !!user,
  });

  const userTodayAppointments = todayAppointments.filter(apt => 
    apt.patientId === user?.id && apt.status === "scheduled"
  );

  const checkInMutation = useMutation({
    mutationFn: async (appointmentId: string) => {
      const res = await apiRequest("POST", `/api/appointments/${appointmentId}/checkin`);
      return await res.json();
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Successfully checked in for your appointment!",
      });
      queryClient.invalidateQueries({ queryKey: ["/api/appointments/date"] });
      queryClient.invalidateQueries({ queryKey: ["/api/appointments/patient"] });
    },
    onError: (error: Error) => {
      toast({
        title: "Check-in Failed",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const handleCheckIn = (appointmentId: string) => {
    checkInMutation.mutate(appointmentId);
  };

  const getAppointmentStatus = (appointment: Appointment) => {
    const appointmentTime = new Date(appointment.appointmentDate);
    const now = new Date();
    const timeDiff = appointmentTime.getTime() - now.getTime();
    const minutesDiff = Math.floor(timeDiff / (1000 * 60));

    if (appointment.checkedIn) {
      return { status: "checked-in", label: "Checked In", color: "bg-green-100 text-green-800" };
    } else if (minutesDiff > 30) {
      return { status: "early", label: "Too Early", color: "bg-gray-100 text-gray-800" };
    } else if (minutesDiff <= 30 && minutesDiff >= -15) {
      return { status: "ready", label: "Ready to Check In", color: "bg-blue-100 text-blue-800" };
    } else {
      return { status: "late", label: "Late", color: "bg-red-100 text-red-800" };
    }
  };

  return (
    <div className="flex h-screen overflow-hidden bg-background">
      <Sidebar isOpen={sidebarOpen} onClose={() => setSidebarOpen(false)} />
      
      <main className="flex-1 overflow-auto">
        <Header 
          title="Check-in" 
          subtitle="Check in for your scheduled appointments"
          onMenuClick={() => setSidebarOpen(true)}
        />

        <div className="p-4 lg:p-6 space-y-6">
          {/* Quick Check-in Instructions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <CheckCircle className="h-5 w-5 text-primary" />
                <span>How to Check In</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center space-y-2">
                  <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto">
                    <Calendar className="h-6 w-6 text-primary" />
                  </div>
                  <h3 className="font-medium">Find Your Appointment</h3>
                  <p className="text-sm text-muted-foreground">
                    Locate your scheduled appointment for today
                  </p>
                </div>
                
                <div className="text-center space-y-2">
                  <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto">
                    <Clock className="h-6 w-6 text-primary" />
                  </div>
                  <h3 className="font-medium">Check Timing</h3>
                  <p className="text-sm text-muted-foreground">
                    You can check in 30 minutes before your appointment
                  </p>
                </div>
                
                <div className="text-center space-y-2">
                  <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto">
                    <CheckCircle className="h-6 w-6 text-primary" />
                  </div>
                  <h3 className="font-medium">Complete Check-in</h3>
                  <p className="text-sm text-muted-foreground">
                    Click the check-in button when ready
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Today's Appointments */}
          <Card>
            <CardHeader>
              <CardTitle>Today's Appointments</CardTitle>
              <p className="text-sm text-muted-foreground">
                {new Date().toLocaleDateString('en-US', { 
                  weekday: 'long', 
                  year: 'numeric', 
                  month: 'long', 
                  day: 'numeric' 
                })}
              </p>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="space-y-4">
                  {[...Array(2)].map((_, i) => (
                    <div key={i} className="h-24 bg-muted rounded-lg animate-pulse" />
                  ))}
                </div>
              ) : userTodayAppointments.length === 0 ? (
                <div className="text-center py-8">
                  <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">No appointments today</h3>
                  <p className="text-muted-foreground">
                    You don't have any scheduled appointments for today.
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {userTodayAppointments.map((appointment) => {
                    const appointmentStatus = getAppointmentStatus(appointment);
                    const appointmentTime = new Date(appointment.appointmentDate);
                    
                    return (
                      <div 
                        key={appointment.id} 
                        className="border border-border rounded-lg p-4"
                        data-testid={`appointment-checkin-${appointment.id}`}
                      >
                        <div className="flex items-center justify-between mb-4">
                          <div className="flex items-center space-x-3">
                            <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
                              <User className="h-6 w-6 text-primary" />
                            </div>
                            <div>
                              <h4 className="font-medium" data-testid="text-appointment-type">
                                {appointment.appointmentType.charAt(0).toUpperCase() + 
                                 appointment.appointmentType.slice(1).replace('_', ' ')} 
                                Consultation
                              </h4>
                              <p className="text-sm text-muted-foreground">
                                Provider ID: {appointment.providerId}
                              </p>
                            </div>
                          </div>
                          
                          <Badge className={appointmentStatus.color} data-testid="badge-checkin-status">
                            {appointmentStatus.label}
                          </Badge>
                        </div>

                        <div className="grid grid-cols-2 gap-4 mb-4">
                          <div className="flex items-center space-x-2">
                            <Clock className="h-4 w-4 text-muted-foreground" />
                            <span className="text-sm" data-testid="text-appointment-time">
                              {appointmentTime.toLocaleTimeString([], { 
                                hour: '2-digit', 
                                minute: '2-digit' 
                              })}
                            </span>
                          </div>
                          
                          {appointment.reason && (
                            <div className="flex items-center space-x-2">
                              <span className="text-sm text-muted-foreground">
                                Reason: {appointment.reason}
                              </span>
                            </div>
                          )}
                        </div>

                        <div className="flex justify-end">
                          {appointment.checkedIn ? (
                            <div className="flex items-center space-x-2 text-green-600">
                              <CheckCircle className="h-4 w-4" />
                              <span className="text-sm font-medium">
                                Checked in at {appointment.checkedInAt && 
                                new Date(appointment.checkedInAt).toLocaleTimeString()}
                              </span>
                            </div>
                          ) : appointmentStatus.status === "ready" ? (
                            <Button
                              onClick={() => handleCheckIn(appointment.id)}
                              disabled={checkInMutation.isPending}
                              className="bg-green-600 hover:bg-green-700"
                              data-testid="button-checkin"
                            >
                              <CheckCircle className="h-4 w-4 mr-2" />
                              {checkInMutation.isPending ? "Checking in..." : "Check In"}
                            </Button>
                          ) : appointmentStatus.status === "early" ? (
                            <Button disabled variant="outline" data-testid="button-checkin-early">
                              Check-in opens 30 minutes before
                            </Button>
                          ) : (
                            <Button disabled variant="outline" data-testid="button-checkin-late">
                              Please contact reception
                            </Button>
                          )}
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Check-in Help */}
          <Card>
            <CardHeader>
              <CardTitle>Need Help?</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-primary rounded-full mt-2" />
                  <div>
                    <p className="text-sm font-medium">Can't find your appointment?</p>
                    <p className="text-sm text-muted-foreground">
                      Make sure you're checking in on the correct date. Contact reception if you need assistance.
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-primary rounded-full mt-2" />
                  <div>
                    <p className="text-sm font-medium">Running late?</p>
                    <p className="text-sm text-muted-foreground">
                      Please call the clinic to let us know. We may need to reschedule your appointment.
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-primary rounded-full mt-2" />
                  <div>
                    <p className="text-sm font-medium">Emergency?</p>
                    <p className="text-sm text-muted-foreground">
                      For medical emergencies, please call 911 or go to the nearest emergency room.
                    </p>
                  </div>
                </div>
              </div>

              <div className="mt-6 p-4 bg-muted rounded-lg">
                <div className="flex items-center space-x-2 mb-2">
                  <Clock className="h-4 w-4 text-primary" />
                  <span className="font-medium">Clinic Hours</span>
                </div>
                <p className="text-sm text-muted-foreground">
                  Monday - Friday: 8:00 AM - 5:00 PM<br />
                  Phone: (*************
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
}
