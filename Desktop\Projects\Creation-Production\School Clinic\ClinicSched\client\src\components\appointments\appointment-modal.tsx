import { useState } from "react";
import { useAuth } from "@/hooks/use-auth";
import { useMutation, useQueryClient, useQuery } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { insertAppointmentSchema, type User } from "@shared/schema";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";

interface AppointmentModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedDate?: Date;
  selectedTime?: string;
}

const appointmentFormSchema = insertAppointmentSchema.extend({
  appointmentDateStr: z.string(),
  appointmentTimeStr: z.string(),
}).omit({
  appointmentDate: true,
  patientId: true,
});

type AppointmentFormData = z.infer<typeof appointmentFormSchema>;

export default function AppointmentModal({ 
  isOpen, 
  onClose, 
  selectedDate, 
  selectedTime 
}: AppointmentModalProps) {
  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data: staff = [] } = useQuery<User[]>({
    queryKey: ["/api/staff"],
    enabled: isOpen,
  });

  const form = useForm<AppointmentFormData>({
    resolver: zodResolver(appointmentFormSchema),
    defaultValues: {
      appointmentDateStr: selectedDate?.toISOString().split('T')[0] || "",
      appointmentTimeStr: selectedTime || "",
      appointmentType: "general",
      reason: "",
      notes: "",
      providerId: "",
      status: "scheduled",
    },
  });

  const appointmentMutation = useMutation({
    mutationFn: async (data: AppointmentFormData) => {
      const { appointmentDateStr, appointmentTimeStr, ...appointmentData } = data;
      
      // Combine date and time
      const appointmentDate = new Date(`${appointmentDateStr}T${appointmentTimeStr}`);
      
      const fullAppointmentData = {
        ...appointmentData,
        appointmentDate,
        patientId: user!.id,
      };

      const res = await apiRequest("POST", "/api/appointments", fullAppointmentData);
      return await res.json();
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Appointment booked successfully!",
      });
      queryClient.invalidateQueries({ queryKey: ["/api/appointments/patient"] });
      onClose();
      form.reset();
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const onSubmit = (data: AppointmentFormData) => {
    appointmentMutation.mutate(data);
  };

  const handleClose = () => {
    onClose();
    form.reset();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md" data-testid="modal-appointment">
        <DialogHeader>
          <DialogTitle>Book Appointment</DialogTitle>
        </DialogHeader>

        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="appointmentDate">Date</Label>
              <Input
                id="appointmentDate"
                type="date"
                {...form.register("appointmentDateStr")}
                data-testid="input-appointment-date"
              />
              {form.formState.errors.appointmentDateStr && (
                <p className="text-sm text-destructive">
                  {form.formState.errors.appointmentDateStr.message}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="appointmentTime">Time</Label>
              <Input
                id="appointmentTime"
                type="time"
                {...form.register("appointmentTimeStr")}
                data-testid="input-appointment-time"
              />
              {form.formState.errors.appointmentTimeStr && (
                <p className="text-sm text-destructive">
                  {form.formState.errors.appointmentTimeStr.message}
                </p>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="appointmentType">Appointment Type</Label>
            <Select
              value={form.watch("appointmentType")}
              onValueChange={(value) => form.setValue("appointmentType", value as any)}
            >
              <SelectTrigger data-testid="select-appointment-type">
                <SelectValue placeholder="Select appointment type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="general">General Consultation</SelectItem>
                <SelectItem value="follow_up">Follow-up Visit</SelectItem>
                <SelectItem value="vaccination">Vaccination</SelectItem>
                <SelectItem value="physical">Physical Examination</SelectItem>
                <SelectItem value="mental_health">Mental Health</SelectItem>
                <SelectItem value="emergency">Emergency</SelectItem>
              </SelectContent>
            </Select>
            {form.formState.errors.appointmentType && (
              <p className="text-sm text-destructive">
                {form.formState.errors.appointmentType.message}
              </p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="providerId">Healthcare Provider</Label>
            <Select
              value={form.watch("providerId")}
              onValueChange={(value) => form.setValue("providerId", value)}
            >
              <SelectTrigger data-testid="select-provider">
                <SelectValue placeholder="Select a provider" />
              </SelectTrigger>
              <SelectContent>
                {staff.map((provider) => (
                  <SelectItem key={provider.id} value={provider.id}>
                    Dr. {provider.firstName} {provider.lastName}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {form.formState.errors.providerId && (
              <p className="text-sm text-destructive">
                {form.formState.errors.providerId.message}
              </p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="reason">Reason for Visit</Label>
            <Input
              id="reason"
              {...form.register("reason")}
              placeholder="Brief description of your concern"
              data-testid="input-reason"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="notes">Additional Notes</Label>
            <Textarea
              id="notes"
              {...form.register("notes")}
              placeholder="Any additional information (optional)"
              className="h-20 resize-none"
              data-testid="textarea-notes"
            />
          </div>

          <div className="flex space-x-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              className="flex-1"
              data-testid="button-cancel-appointment"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="flex-1"
              disabled={appointmentMutation.isPending}
              data-testid="button-confirm-appointment"
            >
              {appointmentMutation.isPending ? "Booking..." : "Book Appointment"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
