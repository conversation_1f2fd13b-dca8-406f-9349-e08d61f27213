# Health Management System

## Overview

This is a comprehensive health management system designed for educational institutions, providing a centralized platform for students, staff, and administrators to manage healthcare services. The application facilitates appointment scheduling, medical record management, staff scheduling, inventory tracking, and patient check-ins through a modern web interface.

## User Preferences

Preferred communication style: Simple, everyday language.

## System Architecture

### Frontend Architecture
- **Framework**: React with TypeScript for type-safe development
- **Routing**: Wouter for client-side routing with protected routes for authentication
- **UI Components**: Radix UI components with shadcn/ui design system for consistent styling
- **Styling**: Tailwind CSS with CSS variables for theming and responsive design
- **State Management**: TanStack React Query for server state management and caching
- **Forms**: React Hook Form with Zod for validation and type safety

### Backend Architecture
- **Runtime**: Node.js with Express.js framework
- **Language**: TypeScript for full-stack type safety
- **API Design**: RESTful API with role-based access control
- **Authentication**: Passport.js with local strategy using session-based authentication
- **Security**: Password hashing with scrypt, session management with express-session
- **Middleware**: Custom logging, error handling, and authentication middleware

### Database Architecture
- **ORM**: Drizzle ORM for type-safe database operations
- **Database**: PostgreSQL with Neon serverless provider
- **Schema Design**: 
  - User management with role-based permissions (student/staff/admin)
  - Appointment scheduling with status tracking
  - Medical records with patient history
  - Staff scheduling with availability management
  - Inventory tracking with stock status monitoring
- **Migrations**: Drizzle-kit for schema migrations and version control

### Authentication & Authorization
- **Strategy**: Session-based authentication with PostgreSQL session store
- **Password Security**: Scrypt hashing with salt for secure password storage
- **Role-Based Access**: Three-tier permission system (student, staff, admin)
- **Protected Routes**: Client-side route protection with server-side validation
- **Session Management**: Express-session with connect-pg-simple for persistence

### Development & Build Architecture
- **Build Tool**: Vite for fast development and optimized production builds
- **Development**: Hot module replacement with error overlay for debugging
- **TypeScript**: Shared types between client and server for consistency
- **Path Aliases**: Configured aliases for clean imports (@/, @shared/)
- **Code Quality**: ESLint and TypeScript strict mode for code consistency

## External Dependencies

### Database Services
- **Neon Database**: Serverless PostgreSQL provider for cloud database hosting
- **connect-pg-simple**: PostgreSQL session store for Express sessions

### Authentication
- **Passport.js**: Authentication middleware with local strategy
- **express-session**: Session management middleware

### UI & Styling
- **Radix UI**: Headless UI component library for accessibility
- **Tailwind CSS**: Utility-first CSS framework
- **Lucide Icons**: Icon library for consistent iconography
- **shadcn/ui**: Pre-built component system based on Radix UI

### Development Tools
- **Vite**: Build tool and development server
- **TypeScript**: Static type checking
- **Drizzle ORM**: Type-safe database toolkit
- **React Hook Form**: Form handling library
- **Zod**: Schema validation library
- **TanStack React Query**: Data fetching and caching
- **Wouter**: Lightweight React router

### Utilities
- **date-fns**: Date manipulation library
- **class-variance-authority**: Utility for managing CSS class variants
- **clsx**: Conditional CSS class utility