import { useState } from "react";
import { useAuth } from "@/hooks/use-auth";
import { useQuery } from "@tanstack/react-query";
import Sidebar from "@/components/layout/sidebar";
import Header from "@/components/layout/header";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { FileText, Search, Calendar, User, Stethoscope } from "lucide-react";
import type { MedicalRecord } from "@shared/schema";

export default function MedicalRecords() {
  const { user } = useAuth();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [typeFilter, setTypeFilter] = useState("all");

  const { data: medicalRecords = [], isLoading } = useQuery<MedicalRecord[]>({
    queryKey: ["/api/medical-records/patient", user?.id],
    enabled: !!user?.id,
  });

  const filteredRecords = medicalRecords.filter(record => {
    const matchesSearch = !searchTerm || 
      record.chiefComplaint?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      record.diagnosis?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      record.treatment?.toLowerCase().includes(searchTerm.toLowerCase());

    return matchesSearch;
  });

  const getStatusColor = (followUpRequired: boolean) => {
    return followUpRequired 
      ? "bg-yellow-100 text-yellow-800" 
      : "bg-green-100 text-green-800";
  };

  return (
    <div className="flex h-screen overflow-hidden bg-background">
      <Sidebar isOpen={sidebarOpen} onClose={() => setSidebarOpen(false)} />
      
      <main className="flex-1 overflow-auto">
        <Header 
          title="Medical Records" 
          subtitle="View your medical history and treatment records"
          onMenuClick={() => setSidebarOpen(true)}
        />

        <div className="p-4 lg:p-6 space-y-6">
          {/* Search and Filters */}
          <Card>
            <CardContent className="p-4">
              <div className="flex flex-col md:flex-row md:items-center space-y-4 md:space-y-0 md:space-x-4">
                <div className="flex-1 relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search records by complaint, diagnosis, or treatment..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                    data-testid="input-search-records"
                  />
                </div>
                <div className="flex space-x-2">
                  <Select value={typeFilter} onValueChange={setTypeFilter}>
                    <SelectTrigger className="w-40" data-testid="select-type-filter">
                      <SelectValue placeholder="Filter by type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Records</SelectItem>
                      <SelectItem value="recent">Recent Visits</SelectItem>
                      <SelectItem value="follow-up">Pending Follow-ups</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Medical Records List */}
          <div className="space-y-4">
            {isLoading ? (
              <div className="space-y-4">
                {[...Array(3)].map((_, i) => (
                  <div key={i} className="h-32 bg-muted rounded-lg animate-pulse" />
                ))}
              </div>
            ) : filteredRecords.length === 0 ? (
              <Card>
                <CardContent className="p-8 text-center">
                  <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">No medical records found</h3>
                  <p className="text-muted-foreground">
                    {searchTerm 
                      ? "Try adjusting your search terms" 
                      : "Your medical records will appear here after your visits"
                    }
                  </p>
                </CardContent>
              </Card>
            ) : (
              filteredRecords.map((record) => (
                <Card key={record.id} data-testid={`medical-record-${record.id}`}>
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div className="space-y-1">
                        <div className="flex items-center space-x-2">
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm font-medium" data-testid="text-visit-date">
                            {new Date(record.visitDate).toLocaleDateString('en-US', {
                              weekday: 'long',
                              year: 'numeric',
                              month: 'long',
                              day: 'numeric'
                            })}
                          </span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <User className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm text-muted-foreground">
                            Provider ID: {record.providerId}
                          </span>
                        </div>
                      </div>
                      
                      <Badge 
                        className={getStatusColor(record.followUpRequired || false)}
                        data-testid="badge-follow-up-status"
                      >
                        {record.followUpRequired ? "Follow-up Required" : "Completed"}
                      </Badge>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-4">
                        {record.chiefComplaint && (
                          <div>
                            <h4 className="text-sm font-medium text-foreground mb-1">
                              Chief Complaint
                            </h4>
                            <p className="text-sm text-muted-foreground" data-testid="text-chief-complaint">
                              {record.chiefComplaint}
                            </p>
                          </div>
                        )}

                        {record.diagnosis && (
                          <div>
                            <h4 className="text-sm font-medium text-foreground mb-1">
                              Diagnosis
                            </h4>
                            <p className="text-sm text-muted-foreground" data-testid="text-diagnosis">
                              {record.diagnosis}
                            </p>
                          </div>
                        )}
                      </div>

                      <div className="space-y-4">
                        {record.treatment && (
                          <div>
                            <h4 className="text-sm font-medium text-foreground mb-1">
                              Treatment
                            </h4>
                            <p className="text-sm text-muted-foreground" data-testid="text-treatment">
                              {record.treatment}
                            </p>
                          </div>
                        )}

                        {record.prescriptions && (
                          <div>
                            <h4 className="text-sm font-medium text-foreground mb-1 flex items-center space-x-1">
                              <Stethoscope className="h-3 w-3" />
                              <span>Prescriptions</span>
                            </h4>
                            <p className="text-sm text-muted-foreground" data-testid="text-prescriptions">
                              {record.prescriptions}
                            </p>
                          </div>
                        )}

                        {record.followUpDate && (
                          <div>
                            <h4 className="text-sm font-medium text-foreground mb-1">
                              Follow-up Date
                            </h4>
                            <p className="text-sm text-muted-foreground" data-testid="text-follow-up-date">
                              {new Date(record.followUpDate).toLocaleDateString()}
                            </p>
                          </div>
                        )}
                      </div>
                    </div>

                    {record.vitals && (
                      <div className="mt-4 pt-4 border-t border-border">
                        <h4 className="text-sm font-medium text-foreground mb-2">Vital Signs</h4>
                        <div className="bg-muted p-3 rounded-md">
                          <pre className="text-sm text-muted-foreground whitespace-pre-wrap">
                            {record.vitals}
                          </pre>
                        </div>
                      </div>
                    )}

                    <div className="flex justify-end mt-4 pt-4 border-t border-border">
                      <Button variant="outline" size="sm" data-testid="button-view-full-record">
                        View Full Record
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        </div>
      </main>
    </div>
  );
}
