import { useAuth } from "@/hooks/use-auth";
import { useLocation } from "wouter";
import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { 
  Heart, 
  LayoutDashboard, 
  Calendar, 
  User, 
  FileText, 
  CheckCircle, 
  Clock, 
  Users, 
  Package, 
  BarChart3, 
  LogOut,
  X
} from "lucide-react";

interface SidebarProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function Sidebar({ isOpen, onClose }: SidebarProps) {
  const { user, logoutMutation } = useAuth();
  const [location, setLocation] = useLocation();

  const navigation = [
    { name: "Dashboard", href: "/", icon: LayoutDashboard, roles: ["student", "staff", "admin"] },
    { name: "Appointments", href: "/appointments", icon: Calendar, roles: ["student", "staff", "admin"] },
    { name: "My Profile", href: "/profile", icon: User, roles: ["student", "staff", "admin"] },
    { name: "Medical Records", href: "/records", icon: FileText, roles: ["student", "staff", "admin"] },
    { name: "Check-in", href: "/checkin", icon: CheckCircle, roles: ["student", "staff", "admin"] },
  ];

  const staffNavigation = [
    { name: "Schedule", href: "/staff/schedule", icon: Clock, roles: ["staff", "admin"] },
    { name: "Patient List", href: "/staff/patients", icon: Users, roles: ["staff", "admin"] },
    { name: "Inventory", href: "/staff/inventory", icon: Package, roles: ["staff", "admin"] },
    { name: "Analytics", href: "/staff/analytics", icon: BarChart3, roles: ["staff", "admin"] },
  ];

  const filteredNavigation = navigation.filter(item => 
    item.roles.includes(user?.role || "student")
  );

  const filteredStaffNavigation = staffNavigation.filter(item => 
    item.roles.includes(user?.role || "student")
  );

  const handleNavigation = (href: string) => {
    setLocation(href);
    onClose();
  };

  const handleLogout = () => {
    logoutMutation.mutate();
  };

  return (
    <>
      {/* Mobile overlay */}
      {isOpen && (
        <div 
          className="fixed inset-0 z-40 bg-background/80 backdrop-blur-sm lg:hidden"
          onClick={onClose}
        />
      )}

      {/* Sidebar */}
      <aside
        className={cn(
          "fixed inset-y-0 left-0 z-50 w-64 bg-card border-r border-border transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0",
          isOpen ? "translate-x-0" : "-translate-x-full"
        )}
      >
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="p-6 border-b border-border">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Heart className="h-8 w-8 text-primary" />
                <h1 className="text-xl font-bold text-foreground">HealthCare Connect</h1>
              </div>
              <Button
                variant="ghost"
                size="sm"
                className="lg:hidden"
                onClick={onClose}
                data-testid="button-close-sidebar"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
            <p className="text-sm text-muted-foreground mt-1">School Medical Clinic</p>
          </div>

          {/* User Profile */}
          <div className="p-4 border-b border-border">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-primary rounded-full flex items-center justify-center">
                <span className="text-primary-foreground font-medium">
                  {user?.firstName?.[0]}{user?.lastName?.[0]}
                </span>
              </div>
              <div>
                <p className="font-medium text-sm" data-testid="text-user-name">
                  {user?.firstName} {user?.lastName}
                </p>
                <p className="text-xs text-muted-foreground capitalize" data-testid="text-user-role">
                  {user?.role}
                </p>
              </div>
            </div>
          </div>

          {/* Navigation */}
          <nav className="flex-1 p-4 space-y-2">
            {filteredNavigation.map((item) => {
              const Icon = item.icon;
              const isActive = location === item.href;
              
              return (
                <button
                  key={item.name}
                  onClick={() => handleNavigation(item.href)}
                  className={cn(
                    "flex items-center space-x-3 p-3 rounded-md transition-colors w-full text-left",
                    isActive
                      ? "bg-primary text-primary-foreground"
                      : "text-muted-foreground hover:bg-muted hover:text-foreground"
                  )}
                  data-testid={`nav-${item.name.toLowerCase().replace(" ", "-")}`}
                >
                  <Icon className="h-4 w-4" />
                  <span>{item.name}</span>
                </button>
              );
            })}

            {/* Staff Tools Section */}
            {filteredStaffNavigation.length > 0 && (
              <div className="pt-4 border-t border-border">
                <h3 className="text-xs uppercase tracking-wide text-muted-foreground mb-2 px-3">
                  Staff Tools
                </h3>
                {filteredStaffNavigation.map((item) => {
                  const Icon = item.icon;
                  const isActive = location === item.href;
                  
                  return (
                    <button
                      key={item.name}
                      onClick={() => handleNavigation(item.href)}
                      className={cn(
                        "flex items-center space-x-3 p-3 rounded-md transition-colors w-full text-left",
                        isActive
                          ? "bg-primary text-primary-foreground"
                          : "text-muted-foreground hover:bg-muted hover:text-foreground"
                      )}
                      data-testid={`nav-staff-${item.name.toLowerCase().replace(" ", "-")}`}
                    >
                      <Icon className="h-4 w-4" />
                      <span>{item.name}</span>
                    </button>
                  );
                })}
              </div>
            )}
          </nav>

          {/* Logout */}
          <div className="p-4 border-t border-border">
            <Button
              variant="ghost"
              className="w-full justify-start"
              onClick={handleLogout}
              disabled={logoutMutation.isPending}
              data-testid="button-logout"
            >
              <LogOut className="h-4 w-4 mr-3" />
              {logoutMutation.isPending ? "Signing out..." : "Logout"}
            </Button>
          </div>
        </div>
      </aside>
    </>
  );
}
