import { useState } from "react";
import { useAuth } from "@/hooks/use-auth";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import Sidebar from "@/components/layout/sidebar";
import Header from "@/components/layout/header";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Calendar, ChevronLeft, ChevronRight, Plus, Clock, Edit } from "lucide-react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { insertStaffScheduleSchema, type StaffSchedule, type User } from "@shared/schema";
import { z } from "zod";

const scheduleFormSchema = insertStaffScheduleSchema;
type ScheduleFormData = z.infer<typeof scheduleFormSchema>;

const daysOfWeek = [
  { value: 0, label: "Sunday" },
  { value: 1, label: "Monday" },
  { value: 2, label: "Tuesday" },
  { value: 3, label: "Wednesday" },
  { value: 4, label: "Thursday" },
  { value: 5, label: "Friday" },
  { value: 6, label: "Saturday" },
];

const workDays = daysOfWeek.slice(1, 6); // Monday to Friday

export default function StaffSchedule() {
  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [currentWeek, setCurrentWeek] = useState(new Date());
  const [scheduleModalOpen, setScheduleModalOpen] = useState(false);
  const [selectedStaff, setSelectedStaff] = useState<string>("");

  // Redirect non-staff users
  if (user?.role === "student") {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-foreground mb-2">Access Denied</h1>
          <p className="text-muted-foreground">Staff access required</p>
        </div>
      </div>
    );
  }

  const { data: staff = [] } = useQuery<User[]>({
    queryKey: ["/api/staff"],
  });

  const { data: schedules = [] } = useQuery<StaffSchedule[]>({
    queryKey: ["/api/staff-schedules", selectedStaff || user?.id],
    enabled: !!(selectedStaff || user?.id),
  });

  const form = useForm<ScheduleFormData>({
    resolver: zodResolver(scheduleFormSchema),
    defaultValues: {
      staffId: selectedStaff || user?.id || "",
      dayOfWeek: 1,
      startTime: "08:00",
      endTime: "17:00",
      isAvailable: true,
    },
  });

  const createScheduleMutation = useMutation({
    mutationFn: async (data: ScheduleFormData) => {
      const res = await apiRequest("POST", "/api/staff-schedules", data);
      return await res.json();
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Schedule created successfully!",
      });
      queryClient.invalidateQueries({ queryKey: ["/api/staff-schedules"] });
      setScheduleModalOpen(false);
      form.reset();
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const onSubmit = (data: ScheduleFormData) => {
    createScheduleMutation.mutate(data);
  };

  const getScheduleForDay = (dayOfWeek: number) => {
    return schedules.find(schedule => schedule.dayOfWeek === dayOfWeek);
  };

  const getStatusBadge = (schedule?: StaffSchedule) => {
    if (!schedule) {
      return <Badge variant="outline">Not Set</Badge>;
    }
    if (!schedule.isAvailable) {
      return <Badge className="bg-red-100 text-red-700">Off</Badge>;
    }
    return (
      <Badge className="bg-green-100 text-green-700">
        {schedule.startTime} - {schedule.endTime}
      </Badge>
    );
  };

  const getCurrentWeekDates = () => {
    const startOfWeek = new Date(currentWeek);
    startOfWeek.setDate(currentWeek.getDate() - currentWeek.getDay() + 1); // Monday
    
    const dates = [];
    for (let i = 0; i < 5; i++) { // Monday to Friday
      const date = new Date(startOfWeek);
      date.setDate(startOfWeek.getDate() + i);
      dates.push(date);
    }
    return dates;
  };

  const navigateWeek = (direction: 'prev' | 'next') => {
    setCurrentWeek(prev => {
      const newDate = new Date(prev);
      newDate.setDate(prev.getDate() + (direction === 'next' ? 7 : -7));
      return newDate;
    });
  };

  return (
    <div className="flex h-screen overflow-hidden bg-background">
      <Sidebar isOpen={sidebarOpen} onClose={() => setSidebarOpen(false)} />
      
      <main className="flex-1 overflow-auto">
        <Header 
          title="Staff Schedule" 
          subtitle="Manage medical staff schedules and availability"
          onMenuClick={() => setSidebarOpen(true)}
          actions={
            <Dialog open={scheduleModalOpen} onOpenChange={setScheduleModalOpen}>
              <DialogTrigger asChild>
                <Button data-testid="button-add-schedule">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Schedule
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Add Schedule Block</DialogTitle>
                </DialogHeader>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                  {user?.role === "admin" && (
                    <div className="space-y-2">
                      <Label htmlFor="staffId">Staff Member</Label>
                      <Select
                        value={form.watch("staffId")}
                        onValueChange={(value) => form.setValue("staffId", value)}
                      >
                        <SelectTrigger data-testid="select-staff-member">
                          <SelectValue placeholder="Select staff member" />
                        </SelectTrigger>
                        <SelectContent>
                          {staff.map((member) => (
                            <SelectItem key={member.id} value={member.id}>
                              Dr. {member.firstName} {member.lastName}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  )}

                  <div className="space-y-2">
                    <Label htmlFor="dayOfWeek">Day of Week</Label>
                    <Select
                      value={form.watch("dayOfWeek").toString()}
                      onValueChange={(value) => form.setValue("dayOfWeek", parseInt(value))}
                    >
                      <SelectTrigger data-testid="select-day-of-week">
                        <SelectValue placeholder="Select day" />
                      </SelectTrigger>
                      <SelectContent>
                        {workDays.map((day) => (
                          <SelectItem key={day.value} value={day.value.toString()}>
                            {day.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="startTime">Start Time</Label>
                      <Input
                        id="startTime"
                        type="time"
                        {...form.register("startTime")}
                        data-testid="input-start-time"
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="endTime">End Time</Label>
                      <Input
                        id="endTime"
                        type="time"
                        {...form.register("endTime")}
                        data-testid="input-end-time"
                      />
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch
                      id="isAvailable"
                      checked={form.watch("isAvailable")}
                      onCheckedChange={(checked) => form.setValue("isAvailable", checked)}
                      data-testid="switch-is-available"
                    />
                    <Label htmlFor="isAvailable">Available</Label>
                  </div>

                  <div className="flex space-x-3 pt-4">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setScheduleModalOpen(false)}
                      className="flex-1"
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      className="flex-1"
                      disabled={createScheduleMutation.isPending}
                      data-testid="button-save-schedule"
                    >
                      {createScheduleMutation.isPending ? "Saving..." : "Save Schedule"}
                    </Button>
                  </div>
                </form>
              </DialogContent>
            </Dialog>
          }
        />

        <div className="p-4 lg:p-6 space-y-6">
          {/* Staff Selection (Admin only) */}
          {user?.role === "admin" && (
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-4">
                  <Label htmlFor="staff-select">View Schedule For:</Label>
                  <Select value={selectedStaff} onValueChange={setSelectedStaff}>
                    <SelectTrigger className="w-64" data-testid="select-view-staff">
                      <SelectValue placeholder="Select staff member" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">My Schedule</SelectItem>
                      {staff.map((member) => (
                        <SelectItem key={member.id} value={member.id}>
                          Dr. {member.firstName} {member.lastName}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Weekly Schedule View */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center space-x-2">
                  <Calendar className="h-5 w-5" />
                  <span>Weekly Schedule</span>
                </CardTitle>
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => navigateWeek('prev')}
                    data-testid="button-prev-week"
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                  <span className="text-sm font-medium" data-testid="text-current-week">
                    Week of {getCurrentWeekDates()[0].toLocaleDateString()}
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => navigateWeek('next')}
                    data-testid="button-next-week"
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-border">
                      <th className="text-left p-4 text-sm font-medium text-muted-foreground">
                        Day
                      </th>
                      <th className="text-center p-4 text-sm font-medium text-muted-foreground">
                        Schedule
                      </th>
                      <th className="text-center p-4 text-sm font-medium text-muted-foreground">
                        Status
                      </th>
                      <th className="text-center p-4 text-sm font-medium text-muted-foreground">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {workDays.map((day, index) => {
                      const schedule = getScheduleForDay(day.value);
                      const weekDate = getCurrentWeekDates()[index];
                      
                      return (
                        <tr key={day.value} className="border-b border-border">
                          <td className="p-4">
                            <div>
                              <p className="font-medium text-sm">{day.label}</p>
                              <p className="text-xs text-muted-foreground">
                                {weekDate.toLocaleDateString()}
                              </p>
                            </div>
                          </td>
                          <td className="p-4 text-center">
                            {schedule && schedule.isAvailable ? (
                              <span className="text-sm">
                                {schedule.startTime} - {schedule.endTime}
                              </span>
                            ) : (
                              <span className="text-sm text-muted-foreground">
                                {schedule ? "Off" : "Not set"}
                              </span>
                            )}
                          </td>
                          <td className="p-4 text-center">
                            {getStatusBadge(schedule)}
                          </td>
                          <td className="p-4 text-center">
                            <Button
                              variant="ghost"
                              size="sm"
                              data-testid={`button-edit-schedule-${day.value}`}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>

          {/* Schedule Summary */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardContent className="p-6 text-center">
                <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-2">
                  <Clock className="h-6 w-6 text-primary" />
                </div>
                <p className="text-sm text-muted-foreground">Total Hours</p>
                <p className="text-2xl font-bold" data-testid="text-total-hours">
                  {schedules
                    .filter(s => s.isAvailable)
                    .reduce((total, schedule) => {
                      const start = new Date(`1970-01-01T${schedule.startTime}`);
                      const end = new Date(`1970-01-01T${schedule.endTime}`);
                      return total + (end.getTime() - start.getTime()) / (1000 * 60 * 60);
                    }, 0)} hrs/week
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6 text-center">
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-2">
                  <Calendar className="h-6 w-6 text-green-600" />
                </div>
                <p className="text-sm text-muted-foreground">Working Days</p>
                <p className="text-2xl font-bold" data-testid="text-working-days">
                  {schedules.filter(s => s.isAvailable).length}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6 text-center">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-2">
                  <Clock className="h-6 w-6 text-blue-600" />
                </div>
                <p className="text-sm text-muted-foreground">Availability</p>
                <p className="text-2xl font-bold text-blue-600" data-testid="text-availability">
                  {schedules.filter(s => s.isAvailable).length > 0 ? "Available" : "Unavailable"}
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    </div>
  );
}
