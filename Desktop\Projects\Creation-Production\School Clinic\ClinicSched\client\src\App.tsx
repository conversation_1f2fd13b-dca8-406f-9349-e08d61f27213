import { Switch, Route } from "wouter";
import { queryClient } from "./lib/queryClient";
import { QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import { AuthProvider } from "@/hooks/use-auth";
import NotFound from "@/pages/not-found";
import AuthPage from "@/pages/auth-page";
import Dashboard from "@/pages/dashboard";
import Appointments from "@/pages/appointments";
import Profile from "@/pages/profile";
import MedicalRecords from "@/pages/medical-records";
import CheckIn from "@/pages/check-in";
import StaffSchedule from "@/pages/staff/schedule";
import StaffPatients from "@/pages/staff/patients";
import StaffInventory from "@/pages/staff/inventory";
import StaffAnalytics from "@/pages/staff/analytics";
import { ProtectedRoute } from "./lib/protected-route";

function Router() {
  return (
    <Switch>
      <ProtectedRoute path="/" component={Dashboard} />
      <ProtectedRoute path="/appointments" component={Appointments} />
      <ProtectedRoute path="/profile" component={Profile} />
      <ProtectedRoute path="/records" component={MedicalRecords} />
      <ProtectedRoute path="/checkin" component={CheckIn} />
      <ProtectedRoute path="/staff/schedule" component={StaffSchedule} />
      <ProtectedRoute path="/staff/patients" component={StaffPatients} />
      <ProtectedRoute path="/staff/inventory" component={StaffInventory} />
      <ProtectedRoute path="/staff/analytics" component={StaffAnalytics} />
      <Route path="/auth" component={AuthPage} />
      <Route component={NotFound} />
    </Switch>
  );
}

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <TooltipProvider>
          <Toaster />
          <Router />
        </TooltipProvider>
      </AuthProvider>
    </QueryClientProvider>
  );
}

export default App;
