import { useState } from "react";
import { useAuth } from "@/hooks/use-auth";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import Sidebar from "@/components/layout/sidebar";
import Header from "@/components/layout/header";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { 
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { 
  Package, 
  Plus, 
  Search, 
  AlertTriangle, 
  <PERSON>, 
  Pill,
  Syringe,
  Bandage,
  Stethoscope
} from "lucide-react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { insertInventorySchema, type Inventory } from "@shared/schema";
import { z } from "zod";

const inventoryFormSchema = insertInventorySchema;
type InventoryFormData = z.infer<typeof inventoryFormSchema>;

export default function StaffInventory() {
  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [categoryFilter, setCategoryFilter] = useState("all");
  const [statusFilter, setStatusFilter] = useState("all");
  const [inventoryModalOpen, setInventoryModalOpen] = useState(false);

  // Redirect non-staff users
  if (user?.role === "student") {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-foreground mb-2">Access Denied</h1>
          <p className="text-muted-foreground">Staff access required</p>
        </div>
      </div>
    );
  }

  const { data: inventory = [], isLoading } = useQuery<Inventory[]>({
    queryKey: ["/api/inventory"],
  });

  const form = useForm<InventoryFormData>({
    resolver: zodResolver(inventoryFormSchema),
    defaultValues: {
      itemName: "",
      category: "Medication",
      description: "",
      currentStock: 0,
      minimumRequired: 0,
      status: "good",
    },
  });

  const createInventoryMutation = useMutation({
    mutationFn: async (data: InventoryFormData) => {
      const res = await apiRequest("POST", "/api/inventory", data);
      return await res.json();
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Inventory item added successfully!",
      });
      queryClient.invalidateQueries({ queryKey: ["/api/inventory"] });
      setInventoryModalOpen(false);
      form.reset();
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const onSubmit = (data: InventoryFormData) => {
    // Calculate status based on stock levels
    let status: "good" | "low_stock" | "out_of_stock" = "good";
    if (data.currentStock === 0) {
      status = "out_of_stock";
    } else if (data.currentStock <= data.minimumRequired) {
      status = "low_stock";
    }

    createInventoryMutation.mutate({ ...data, status });
  };

  const filteredInventory = inventory.filter(item => {
    const matchesSearch = !searchTerm || 
      item.itemName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.description?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesCategory = categoryFilter === "all" || item.category === categoryFilter;
    const matchesStatus = statusFilter === "all" || item.status === statusFilter;

    return matchesSearch && matchesCategory && matchesStatus;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case "good":
        return "bg-green-100 text-green-700";
      case "low_stock":
        return "bg-yellow-100 text-yellow-700";
      case "out_of_stock":
        return "bg-red-100 text-red-700";
      default:
        return "bg-gray-100 text-gray-700";
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case "good":
        return "Good";
      case "low_stock":
        return "Low Stock";
      case "out_of_stock":
        return "Out of Stock";
      default:
        return status;
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category.toLowerCase()) {
      case "medication":
        return <Pill className="h-4 w-4" />;
      case "vaccine":
        return <Syringe className="h-4 w-4" />;
      case "supply":
        return <Bandage className="h-4 w-4" />;
      case "equipment":
        return <Stethoscope className="h-4 w-4" />;
      default:
        return <Package className="h-4 w-4" />;
    }
  };

  const lowStockItems = inventory.filter(item => item.status === "low_stock" || item.status === "out_of_stock");

  return (
    <div className="flex h-screen overflow-hidden bg-background">
      <Sidebar isOpen={sidebarOpen} onClose={() => setSidebarOpen(false)} />
      
      <main className="flex-1 overflow-auto">
        <Header 
          title="Medical Inventory" 
          subtitle="Track and manage medical supplies and equipment"
          onMenuClick={() => setSidebarOpen(true)}
          actions={
            user?.role === "admin" ? (
              <Dialog open={inventoryModalOpen} onOpenChange={setInventoryModalOpen}>
                <DialogTrigger asChild>
                  <Button data-testid="button-add-inventory">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Item
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Add Inventory Item</DialogTitle>
                  </DialogHeader>
                  <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="itemName">Item Name</Label>
                      <Input
                        id="itemName"
                        {...form.register("itemName")}
                        placeholder="e.g., Ibuprofen 200mg"
                        data-testid="input-item-name"
                      />
                      {form.formState.errors.itemName && (
                        <p className="text-sm text-destructive">
                          {form.formState.errors.itemName.message}
                        </p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="category">Category</Label>
                      <Select
                        value={form.watch("category")}
                        onValueChange={(value) => form.setValue("category", value)}
                      >
                        <SelectTrigger data-testid="select-category">
                          <SelectValue placeholder="Select category" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Medication">Medication</SelectItem>
                          <SelectItem value="Vaccine">Vaccine</SelectItem>
                          <SelectItem value="Supply">Medical Supply</SelectItem>
                          <SelectItem value="Equipment">Equipment</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="description">Description</Label>
                      <Input
                        id="description"
                        {...form.register("description")}
                        placeholder="Brief description of the item"
                        data-testid="input-description"
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="currentStock">Current Stock</Label>
                        <Input
                          id="currentStock"
                          type="number"
                          {...form.register("currentStock", { valueAsNumber: true })}
                          min="0"
                          data-testid="input-current-stock"
                        />
                      </div>
                      
                      <div className="space-y-2">
                        <Label htmlFor="minimumRequired">Minimum Required</Label>
                        <Input
                          id="minimumRequired"
                          type="number"
                          {...form.register("minimumRequired", { valueAsNumber: true })}
                          min="0"
                          data-testid="input-minimum-required"
                        />
                      </div>
                    </div>

                    <div className="flex space-x-3 pt-4">
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => setInventoryModalOpen(false)}
                        className="flex-1"
                      >
                        Cancel
                      </Button>
                      <Button
                        type="submit"
                        className="flex-1"
                        disabled={createInventoryMutation.isPending}
                        data-testid="button-save-inventory"
                      >
                        {createInventoryMutation.isPending ? "Adding..." : "Add Item"}
                      </Button>
                    </div>
                  </form>
                </DialogContent>
              </Dialog>
            ) : null
          }
        />

        <div className="p-4 lg:p-6 space-y-6">
          {/* Inventory Overview */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-6 text-center">
                <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-2">
                  <Pill className="h-6 w-6 text-primary" />
                </div>
                <p className="text-sm text-muted-foreground">Medications</p>
                <p className="text-2xl font-bold" data-testid="text-medication-count">
                  {inventory.filter(item => item.category === "Medication").length}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6 text-center">
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-2">
                  <Syringe className="h-6 w-6 text-green-600" />
                </div>
                <p className="text-sm text-muted-foreground">Vaccines</p>
                <p className="text-2xl font-bold" data-testid="text-vaccine-count">
                  {inventory.filter(item => item.category === "Vaccine").length}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6 text-center">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-2">
                  <Bandage className="h-6 w-6 text-blue-600" />
                </div>
                <p className="text-sm text-muted-foreground">Supplies</p>
                <p className="text-2xl font-bold" data-testid="text-supply-count">
                  {inventory.filter(item => item.category === "Supply").length}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6 text-center">
                <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-2">
                  <AlertTriangle className="h-6 w-6 text-red-600" />
                </div>
                <p className="text-sm text-muted-foreground">Low Stock</p>
                <p className="text-2xl font-bold" data-testid="text-low-stock-count">
                  {lowStockItems.length}
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Search and Filters */}
          <Card>
            <CardContent className="p-4">
              <div className="flex flex-col md:flex-row md:items-center space-y-4 md:space-y-0 md:space-x-4">
                <div className="flex-1 relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search inventory items..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                    data-testid="input-search-inventory"
                  />
                </div>
                <div className="flex space-x-2">
                  <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                    <SelectTrigger className="w-40" data-testid="select-category-filter">
                      <SelectValue placeholder="Category" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Categories</SelectItem>
                      <SelectItem value="Medication">Medication</SelectItem>
                      <SelectItem value="Vaccine">Vaccine</SelectItem>
                      <SelectItem value="Supply">Supply</SelectItem>
                      <SelectItem value="Equipment">Equipment</SelectItem>
                    </SelectContent>
                  </Select>
                  
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger className="w-40" data-testid="select-status-filter">
                      <SelectValue placeholder="Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Status</SelectItem>
                      <SelectItem value="good">Good</SelectItem>
                      <SelectItem value="low_stock">Low Stock</SelectItem>
                      <SelectItem value="out_of_stock">Out of Stock</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Inventory List */}
          <Card>
            <CardHeader>
              <CardTitle>Inventory Items</CardTitle>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="space-y-4">
                  {[...Array(5)].map((_, i) => (
                    <div key={i} className="h-16 bg-muted rounded-lg animate-pulse" />
                  ))}
                </div>
              ) : filteredInventory.length === 0 ? (
                <div className="text-center py-8">
                  <Package className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">No inventory items found</h3>
                  <p className="text-muted-foreground">
                    {searchTerm ? "Try adjusting your search terms" : "Start by adding your first inventory item"}
                  </p>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b border-border">
                        <th className="text-left p-4 text-sm font-medium text-muted-foreground">Item</th>
                        <th className="text-left p-4 text-sm font-medium text-muted-foreground">Category</th>
                        <th className="text-center p-4 text-sm font-medium text-muted-foreground">Stock</th>
                        <th className="text-center p-4 text-sm font-medium text-muted-foreground">Min. Required</th>
                        <th className="text-center p-4 text-sm font-medium text-muted-foreground">Status</th>
                        <th className="text-center p-4 text-sm font-medium text-muted-foreground">Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {filteredInventory.map((item) => (
                        <tr key={item.id} className="border-b border-border" data-testid={`inventory-item-${item.id}`}>
                          <td className="p-4">
                            <div className="flex items-center space-x-3">
                              <div className="w-8 h-8 bg-muted rounded-md flex items-center justify-center">
                                {getCategoryIcon(item.category)}
                              </div>
                              <div>
                                <p className="font-medium text-sm" data-testid="text-item-name">
                                  {item.itemName}
                                </p>
                                {item.description && (
                                  <p className="text-xs text-muted-foreground">
                                    {item.description}
                                  </p>
                                )}
                              </div>
                            </div>
                          </td>
                          <td className="p-4 text-sm" data-testid="text-item-category">
                            {item.category}
                          </td>
                          <td className="p-4 text-center text-sm font-medium" data-testid="text-current-stock">
                            {item.currentStock}
                          </td>
                          <td className="p-4 text-center text-sm" data-testid="text-minimum-required">
                            {item.minimumRequired}
                          </td>
                          <td className="p-4 text-center">
                            <Badge className={getStatusColor(item.status)} data-testid="badge-item-status">
                              {getStatusLabel(item.status)}
                            </Badge>
                          </td>
                          <td className="p-4 text-center">
                            <Button variant="ghost" size="sm" data-testid="button-edit-item">
                              <Edit className="h-4 w-4" />
                            </Button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
}
