import { useState } from "react";
import { useAuth } from "@/hooks/use-auth";
import { useQuery } from "@tanstack/react-query";
import Sidebar from "@/components/layout/sidebar";
import Header from "@/components/layout/header";
import AppointmentCalendar from "@/components/calendar/appointment-calendar";
import AppointmentModal from "@/components/appointments/appointment-modal";
import AppointmentCard from "@/components/appointments/appointment-card";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Calendar, Clock, HospitalIcon, CheckCircle } from "lucide-react";
import type { Appointment } from "@shared/schema";

export default function Dashboard() {
  const { user } = useAuth();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [appointmentModalOpen, setAppointmentModalOpen] = useState(false);

  const { data: upcomingAppointments = [], isLoading } = useQuery<Appointment[]>({
    queryKey: ["/api/appointments/patient", user?.id],
    enabled: !!user?.id,
  });

  const { data: todayAppointments = [] } = useQuery<Appointment[]>({
    queryKey: ["/api/appointments/date", new Date().toISOString().split('T')[0]],
    enabled: !!user,
  });

  const nextAppointment = upcomingAppointments.find(apt => 
    new Date(apt.appointmentDate) > new Date() && apt.status === "scheduled"
  );

  const availableSlots = 8; // This would come from API
  const averageWaitTime = 15; // This would come from API
  const clinicStatus = "Open"; // This would come from API

  return (
    <div className="flex h-screen overflow-hidden bg-background">
      <Sidebar isOpen={sidebarOpen} onClose={() => setSidebarOpen(false)} />
      
      <main className="flex-1 overflow-auto">
        <Header 
          title="Dashboard" 
          subtitle="Welcome back! Here's what's happening today."
          onMenuClick={() => setSidebarOpen(true)}
          actions={
            <Button 
              onClick={() => setAppointmentModalOpen(true)}
              data-testid="button-book-appointment"
            >
              <Calendar className="h-4 w-4 mr-2" />
              Book Appointment
            </Button>
          }
        />

        <div className="p-4 lg:p-6 space-y-6">
          {/* Quick Stats */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card data-testid="card-next-appointment">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">Next Appointment</p>
                    <p className="text-2xl font-bold text-foreground">
                      {nextAppointment 
                        ? new Date(nextAppointment.appointmentDate).toLocaleString()
                        : "None scheduled"
                      }
                    </p>
                  </div>
                  <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
                    <Calendar className="h-6 w-6 text-primary" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card data-testid="card-available-slots">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">Available Slots</p>
                    <p className="text-2xl font-bold text-foreground">{availableSlots}</p>
                  </div>
                  <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                    <Clock className="h-6 w-6 text-green-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card data-testid="card-wait-time">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">Wait Time</p>
                    <p className="text-2xl font-bold text-foreground">{averageWaitTime} min</p>
                  </div>
                  <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center">
                    <Clock className="h-6 w-6 text-yellow-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card data-testid="card-clinic-status">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">Clinic Status</p>
                    <p className="text-2xl font-bold text-green-600">{clinicStatus}</p>
                  </div>
                  <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                    <HospitalIcon className="h-6 w-6 text-green-600" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Main Content Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Appointment Booking */}
            <div className="lg:col-span-2">
              <Card>
                <CardHeader>
                  <CardTitle>Book New Appointment</CardTitle>
                  <p className="text-sm text-muted-foreground">
                    Schedule your visit to the medical clinic
                  </p>
                </CardHeader>
                <CardContent>
                  <AppointmentCalendar onTimeSlotSelect={() => setAppointmentModalOpen(true)} />
                </CardContent>
              </Card>
            </div>

            {/* Sidebar Content */}
            <div className="space-y-6">
              {/* Upcoming Appointments */}
              <Card>
                <CardHeader>
                  <CardTitle>Upcoming Appointments</CardTitle>
                </CardHeader>
                <CardContent>
                  {isLoading ? (
                    <div className="space-y-3">
                      <div className="h-16 bg-muted rounded-md animate-pulse" />
                      <div className="h-16 bg-muted rounded-md animate-pulse" />
                    </div>
                  ) : upcomingAppointments.length === 0 ? (
                    <div className="text-center py-6">
                      <p className="text-muted-foreground">No upcoming appointments</p>
                      <Button 
                        variant="outline" 
                        size="sm" 
                        className="mt-2"
                        onClick={() => setAppointmentModalOpen(true)}
                        data-testid="button-book-first-appointment"
                      >
                        Book your first appointment
                      </Button>
                    </div>
                  ) : (
                    <div className="space-y-3">
                      {upcomingAppointments.slice(0, 3).map((appointment) => (
                        <AppointmentCard 
                          key={appointment.id} 
                          appointment={appointment} 
                        />
                      ))}
                      {upcomingAppointments.length > 3 && (
                        <Button 
                          variant="ghost" 
                          className="w-full"
                          data-testid="button-view-all-appointments"
                        >
                          View All Appointments
                        </Button>
                      )}
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Clinic Information */}
              <Card>
                <CardHeader>
                  <CardTitle>Clinic Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <HospitalIcon className="h-4 w-4 text-primary" />
                      <span className="text-sm">Student Health Center, Building A</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Clock className="h-4 w-4 text-primary" />
                      <span className="text-sm">Mon-Fri: 8:00 AM - 5:00 PM</span>
                    </div>
                  </div>

                  <div>
                    <h4 className="font-medium text-sm mb-2">Today's Staff</h4>
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 rounded-full bg-green-500" />
                        <span className="text-sm">Dr. Johnson - General Practice</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 rounded-full bg-green-500" />
                        <span className="text-sm">Nurse Williams - Triage</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Quick Check-in */}
              {nextAppointment && (
                <Card>
                  <CardHeader>
                    <CardTitle>Quick Check-in</CardTitle>
                    <p className="text-xs text-muted-foreground">
                      For scheduled appointments
                    </p>
                  </CardHeader>
                  <CardContent>
                    <Button 
                      className="w-full bg-green-600 hover:bg-green-700"
                      data-testid="button-quick-checkin"
                    >
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Check-in for {new Date(nextAppointment.appointmentDate).toLocaleTimeString()}
                    </Button>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        </div>
      </main>

      <AppointmentModal 
        isOpen={appointmentModalOpen}
        onClose={() => setAppointmentModalOpen(false)}
      />
    </div>
  );
}
