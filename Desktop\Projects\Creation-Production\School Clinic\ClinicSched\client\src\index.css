@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: hsl(0 0% 100%);
  --foreground: hsl(222.2 84% 4.9%);
  --card: hsl(0 0% 100%);
  --card-foreground: hsl(222.2 84% 4.9%);
  --popover: hsl(0 0% 100%);
  --popover-foreground: hsl(222.2 84% 4.9%);
  --primary: hsl(221.2 83.2% 53.3%);
  --primary-foreground: hsl(210 40% 98%);
  --secondary: hsl(210 40% 96%);
  --secondary-foreground: hsl(222.2 84% 4.9%);
  --muted: hsl(210 40% 96%);
  --muted-foreground: hsl(215.4 16.3% 46.9%);
  --accent: hsl(210 40% 96%);
  --accent-foreground: hsl(222.2 84% 4.9%);
  --destructive: hsl(0 84.2% 60.2%);
  --destructive-foreground: hsl(210 40% 98%);
  --border: hsl(214.3 31.8% 91.4%);
  --input: hsl(214.3 31.8% 91.4%);
  --ring: hsl(221.2 83.2% 53.3%);
  --chart-1: hsl(221.2 83.2% 53.3%);
  --chart-2: hsl(159.7826 100% 36.0784%);
  --chart-3: hsl(42.0290 92.8251% 56.2745%);
  --chart-4: hsl(147.1429 78.5047% 41.9608%);
  --chart-5: hsl(341.4894 75.2000% 50.9804%);
  --sidebar: hsl(0 0% 100%);
  --sidebar-foreground: hsl(222.2 84% 4.9%);
  --sidebar-primary: hsl(221.2 83.2% 53.3%);
  --sidebar-primary-foreground: hsl(210 40% 98%);
  --sidebar-accent: hsl(210 40% 96%);
  --sidebar-accent-foreground: hsl(222.2 84% 4.9%);
  --sidebar-border: hsl(214.3 31.8% 91.4%);
  --sidebar-ring: hsl(221.2 83.2% 53.3%);
  --font-sans: 'Inter', sans-serif;
  --font-serif: Georgia, serif;
  --font-mono: 'JetBrains Mono', monospace;
  --radius: 0.75rem;
  --shadow-2xs: 0px 1px 2px 0px hsl(221.2 83.2% 53.3% / 0.05);
  --shadow-xs: 0px 1px 2px 0px hsl(221.2 83.2% 53.3% / 0.05);
  --shadow-sm: 0px 1px 3px 0px hsl(221.2 83.2% 53.3% / 0.1), 0px 1px 2px -1px hsl(221.2 83.2% 53.3% / 0.1);
  --shadow: 0px 1px 3px 0px hsl(221.2 83.2% 53.3% / 0.1), 0px 1px 2px -1px hsl(221.2 83.2% 53.3% / 0.1);
  --shadow-md: 0px 4px 6px -1px hsl(221.2 83.2% 53.3% / 0.1), 0px 2px 4px -2px hsl(221.2 83.2% 53.3% / 0.1);
  --shadow-lg: 0px 10px 15px -3px hsl(221.2 83.2% 53.3% / 0.1), 0px 4px 6px -4px hsl(221.2 83.2% 53.3% / 0.1);
  --shadow-xl: 0px 20px 25px -5px hsl(221.2 83.2% 53.3% / 0.1), 0px 8px 10px -6px hsl(221.2 83.2% 53.3% / 0.1);
  --shadow-2xl: 0px 25px 50px -12px hsl(221.2 83.2% 53.3% / 0.25);
  --tracking-normal: 0em;
  --spacing: 0.25rem;
}

.dark {
  --background: hsl(222.2 84% 4.9%);
  --foreground: hsl(210 40% 98%);
  --card: hsl(222.2 84% 4.9%);
  --card-foreground: hsl(210 40% 98%);
  --popover: hsl(222.2 84% 4.9%);
  --popover-foreground: hsl(210 40% 98%);
  --primary: hsl(221.2 83.2% 53.3%);
  --primary-foreground: hsl(222.2 84% 4.9%);
  --secondary: hsl(217.2 32.6% 17.5%);
  --secondary-foreground: hsl(210 40% 98%);
  --muted: hsl(217.2 32.6% 17.5%);
  --muted-foreground: hsl(215 20.2% 65.1%);
  --accent: hsl(217.2 32.6% 17.5%);
  --accent-foreground: hsl(210 40% 98%);
  --destructive: hsl(0 62.8% 30.6%);
  --destructive-foreground: hsl(210 40% 98%);
  --border: hsl(217.2 32.6% 17.5%);
  --input: hsl(217.2 32.6% 17.5%);
  --ring: hsl(221.2 83.2% 53.3%);
  --chart-1: hsl(220 70% 50%);
  --chart-2: hsl(160 60% 45%);
  --chart-3: hsl(30 80% 55%);
  --chart-4: hsl(280 65% 60%);
  --chart-5: hsl(340 75% 55%);
  --sidebar: hsl(222.2 84% 4.9%);
  --sidebar-foreground: hsl(210 40% 98%);
  --sidebar-primary: hsl(221.2 83.2% 53.3%);
  --sidebar-primary-foreground: hsl(222.2 84% 4.9%);
  --sidebar-accent: hsl(217.2 32.6% 17.5%);
  --sidebar-accent-foreground: hsl(210 40% 98%);
  --sidebar-border: hsl(217.2 32.6% 17.5%);
  --sidebar-ring: hsl(221.2 83.2% 53.3%);
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
    font-family: var(--font-sans);
  }

  /* Medical-specific styling */
  .medical-card {
    border-left: 4px solid var(--primary);
  }

  .status-available {
    background-color: hsl(142.1 76.2% 36.3%);
  }

  .status-busy {
    background-color: hsl(0 84.2% 60.2%);
  }

  .status-break {
    background-color: hsl(45.4 93.4% 47.5%);
  }

  /* Calendar specific styles */
  .calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 1px;
  }

  .time-slot:hover {
    background-color: hsl(221.2 83.2% 95%);
  }

  /* Sidebar transitions */
  .sidebar-transition {
    transition: transform 0.3s ease-in-out;
  }

  @media (max-width: 768px) {
    .sidebar-hidden {
      transform: translateX(-100%);
    }
  }

  /* Healthcare-specific accessibility improvements */
  .medical-alert {
    border: 2px solid hsl(0 84.2% 60.2%);
    background: hsl(0 84.2% 60.2% / 0.1);
  }

  .medical-success {
    border: 2px solid hsl(142.1 76.2% 36.3%);
    background: hsl(142.1 76.2% 36.3% / 0.1);
  }

  /* Focus styles for accessibility */
  .focus-visible:focus-visible {
    outline: 2px solid var(--ring);
    outline-offset: 2px;
  }

  /* Print styles for medical records */
  @media print {
    .no-print {
      display: none !important;
    }
    
    .medical-record {
      page-break-inside: avoid;
      break-inside: avoid;
    }
  }
}

@layer components {
  /* Medical appointment card styling */
  .appointment-card {
    @apply medical-card bg-card p-4 rounded-lg border border-border;
  }

  /* Status indicators */
  .status-indicator {
    @apply inline-flex items-center px-2 py-1 rounded-full text-xs font-medium;
  }

  .status-scheduled {
    @apply bg-blue-100 text-blue-800;
  }

  .status-completed {
    @apply bg-green-100 text-green-800;
  }

  .status-cancelled {
    @apply bg-red-100 text-red-800;
  }

  .status-no-show {
    @apply bg-gray-100 text-gray-800;
  }

  /* Medical form styling */
  .medical-form {
    @apply space-y-6;
  }

  .medical-form .form-section {
    @apply space-y-4 p-4 border border-border rounded-lg;
  }

  .medical-form .form-section h3 {
    @apply text-lg font-semibold text-foreground border-b border-border pb-2;
  }

  /* Inventory status colors */
  .inventory-good {
    @apply bg-green-100 text-green-700;
  }

  .inventory-low {
    @apply bg-yellow-100 text-yellow-700;
  }

  .inventory-out {
    @apply bg-red-100 text-red-700;
  }
}

@layer utilities {
  /* Medical spacing utilities */
  .medical-spacing {
    @apply space-y-4;
  }

  .medical-grid {
    @apply grid gap-4;
  }

  /* Healthcare color utilities */
  .text-medical-primary {
    color: var(--primary);
  }

  .bg-medical-primary {
    background-color: var(--primary);
  }

  .border-medical-primary {
    border-color: var(--primary);
  }

  /* Animation utilities for medical UI */
  .pulse-medical {
    animation: pulse-medical 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  @keyframes pulse-medical {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: .5;
    }
  }

  /* Responsive medical utilities */
  .medical-container {
    @apply container mx-auto px-4 sm:px-6 lg:px-8;
  }

  .medical-section {
    @apply py-8 sm:py-12 lg:py-16;
  }
}

/* Scrollbar styling for medical interface */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--muted);
}

::-webkit-scrollbar-thumb {
  background: var(--muted-foreground);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary);
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --border: hsl(0 0% 50%);
    --primary: hsl(221.2 83.2% 40%);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .sidebar-transition,
  .pulse-medical,
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
