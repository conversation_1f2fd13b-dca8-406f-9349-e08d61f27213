import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Calendar, Clock, User, MoreVertical } from "lucide-react";
import type { Appointment } from "@shared/schema";

interface AppointmentCardProps {
  appointment: Appointment;
  showActions?: boolean;
}

export default function AppointmentCard({ appointment, showActions = false }: AppointmentCardProps) {
  const appointmentDate = new Date(appointment.appointmentDate);
  
  const getStatusColor = (status: string) => {
    switch (status) {
      case "scheduled":
        return "bg-blue-100 text-blue-800";
      case "completed":
        return "bg-green-100 text-green-800";
      case "cancelled":
        return "bg-red-100 text-red-800";
      case "no_show":
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case "general":
        return "General Consultation";
      case "follow_up":
        return "Follow-up Visit";
      case "vaccination":
        return "Vaccination";
      case "physical":
        return "Physical Examination";
      case "mental_health":
        return "Mental Health";
      case "emergency":
        return "Emergency";
      default:
        return type;
    }
  };

  return (
    <Card className="border-l-4 border-l-primary" data-testid={`appointment-card-${appointment.id}`}>
      <CardContent className="p-4">
        <div className="flex items-start justify-between mb-3">
          <div className="space-y-1">
            <h4 className="font-medium text-sm" data-testid="text-appointment-type">
              {getTypeLabel(appointment.appointmentType)}
            </h4>
            <div className="flex items-center space-x-2 text-xs text-muted-foreground">
              <Calendar className="h-3 w-3" />
              <span data-testid="text-appointment-date">
                {appointmentDate.toLocaleDateString()}
              </span>
              <Clock className="h-3 w-3 ml-2" />
              <span data-testid="text-appointment-time">
                {appointmentDate.toLocaleTimeString([], { 
                  hour: '2-digit', 
                  minute: '2-digit' 
                })}
              </span>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <Badge 
              className={getStatusColor(appointment.status)}
              data-testid="badge-appointment-status"
            >
              {appointment.status}
            </Badge>
            {showActions && (
              <Button variant="ghost" size="sm" data-testid="button-appointment-actions">
                <MoreVertical className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>

        {appointment.reason && (
          <p className="text-sm text-muted-foreground mb-2" data-testid="text-appointment-reason">
            {appointment.reason}
          </p>
        )}

        {appointment.checkedIn && (
          <div className="flex items-center space-x-1 text-xs text-green-600">
            <div className="w-2 h-2 bg-green-600 rounded-full" />
            <span>Checked in</span>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
