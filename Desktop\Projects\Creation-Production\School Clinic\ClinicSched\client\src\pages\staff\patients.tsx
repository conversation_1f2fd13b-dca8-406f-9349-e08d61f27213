import { useState } from "react";
import { useAuth } from "@/hooks/use-auth";
import { useQuery } from "@tanstack/react-query";
import Sidebar from "@/components/layout/sidebar";
import Header from "@/components/layout/header";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { 
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Search, 
  Users, 
  Calendar, 
  FileText, 
  Phone, 
  Mail, 
  AlertTriangle,
  Clock,
  User,
  MoreVertical
} from "lucide-react";
import type { User as UserType, Appointment, MedicalRecord } from "@shared/schema";

export default function StaffPatients() {
  const { user } = useAuth();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [selectedPatient, setSelectedPatient] = useState<UserType | null>(null);

  // Redirect non-staff users
  if (user?.role === "student") {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-foreground mb-2">Access Denied</h1>
          <p className="text-muted-foreground">Staff access required</p>
        </div>
      </div>
    );
  }

  const { data: todayAppointments = [] } = useQuery<Appointment[]>({
    queryKey: ["/api/appointments/date", new Date().toISOString().split('T')[0]],
  });

  // In a real app, this would be a separate API endpoint to get all patients
  const { data: patients = [] } = useQuery<UserType[]>({
    queryKey: ["/api/patients"],
    queryFn: async () => {
      // This is a placeholder - in reality you'd have a patients endpoint
      // For now, we'll derive patients from today's appointments
      const uniquePatientIds = [...new Set(todayAppointments.map(apt => apt.patientId))];
      // This would fetch patient details for each ID
      return [];
    },
  });

  const { data: patientAppointments = [] } = useQuery<Appointment[]>({
    queryKey: ["/api/appointments/patient", selectedPatient?.id],
    enabled: !!selectedPatient?.id,
  });

  const { data: patientRecords = [] } = useQuery<MedicalRecord[]>({
    queryKey: ["/api/medical-records/patient", selectedPatient?.id],
    enabled: !!selectedPatient?.id,
  });

  const filteredAppointments = todayAppointments.filter(appointment => {
    const matchesSearch = !searchTerm || 
      appointment.reason?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      appointment.appointmentType.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = statusFilter === "all" || appointment.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case "scheduled":
        return "bg-blue-100 text-blue-800";
      case "completed":
        return "bg-green-100 text-green-800";
      case "cancelled":
        return "bg-red-100 text-red-800";
      case "no_show":
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case "general":
        return "General Consultation";
      case "follow_up":
        return "Follow-up Visit";
      case "vaccination":
        return "Vaccination";
      case "physical":
        return "Physical Examination";
      case "mental_health":
        return "Mental Health";
      case "emergency":
        return "Emergency";
      default:
        return type;
    }
  };

  return (
    <div className="flex h-screen overflow-hidden bg-background">
      <Sidebar isOpen={sidebarOpen} onClose={() => setSidebarOpen(false)} />
      
      <main className="flex-1 overflow-auto">
        <Header 
          title="Patient Management" 
          subtitle="View and manage patient information and appointments"
          onMenuClick={() => setSidebarOpen(true)}
        />

        <div className="p-4 lg:p-6 space-y-6">
          {/* Search and Filters */}
          <Card>
            <CardContent className="p-4">
              <div className="flex flex-col md:flex-row md:items-center space-y-4 md:space-y-0 md:space-x-4">
                <div className="flex-1 relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search patients or appointments..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                    data-testid="input-search-patients"
                  />
                </div>
                <div className="flex space-x-2">
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger className="w-40" data-testid="select-status-filter">
                      <SelectValue placeholder="Filter by status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Statuses</SelectItem>
                      <SelectItem value="scheduled">Scheduled</SelectItem>
                      <SelectItem value="completed">Completed</SelectItem>
                      <SelectItem value="cancelled">Cancelled</SelectItem>
                      <SelectItem value="no_show">No Show</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Today's Appointments */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Calendar className="h-5 w-5" />
                <span>Today's Appointments</span>
                <Badge variant="outline" data-testid="badge-appointment-count">
                  {filteredAppointments.length}
                </Badge>
              </CardTitle>
              <p className="text-sm text-muted-foreground">
                {new Date().toLocaleDateString('en-US', { 
                  weekday: 'long', 
                  year: 'numeric', 
                  month: 'long', 
                  day: 'numeric' 
                })}
              </p>
            </CardHeader>
            <CardContent>
              {filteredAppointments.length === 0 ? (
                <div className="text-center py-8">
                  <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">No appointments found</h3>
                  <p className="text-muted-foreground">
                    {searchTerm ? "Try adjusting your search terms" : "No appointments scheduled for today"}
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {filteredAppointments.map((appointment) => {
                    const appointmentTime = new Date(appointment.appointmentDate);
                    
                    return (
                      <div 
                        key={appointment.id} 
                        className="border border-border rounded-lg p-4"
                        data-testid={`patient-appointment-${appointment.id}`}
                      >
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center space-x-3">
                            <Avatar>
                              <AvatarFallback>
                                <User className="h-4 w-4" />
                              </AvatarFallback>
                            </Avatar>
                            <div>
                              <h4 className="font-medium" data-testid="text-patient-id">
                                Patient ID: {appointment.patientId}
                              </h4>
                              <p className="text-sm text-muted-foreground">
                                {getTypeLabel(appointment.appointmentType)}
                              </p>
                            </div>
                          </div>
                          
                          <div className="flex items-center space-x-2">
                            <Badge className={getStatusColor(appointment.status)} data-testid="badge-appointment-status">
                              {appointment.status}
                            </Badge>
                            {appointment.checkedIn && (
                              <Badge className="bg-green-100 text-green-800">
                                Checked In
                              </Badge>
                            )}
                          </div>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-3">
                          <div className="flex items-center space-x-2">
                            <Clock className="h-4 w-4 text-muted-foreground" />
                            <span className="text-sm" data-testid="text-appointment-time">
                              {appointmentTime.toLocaleTimeString([], { 
                                hour: '2-digit', 
                                minute: '2-digit' 
                              })}
                            </span>
                          </div>
                          
                          {appointment.reason && (
                            <div className="col-span-2">
                              <span className="text-sm text-muted-foreground">
                                Reason: {appointment.reason}
                              </span>
                            </div>
                          )}
                        </div>

                        <div className="flex justify-between items-center">
                          <div className="flex space-x-2">
                            {appointment.checkedIn && (
                              <span className="text-xs text-green-600">
                                Checked in at {appointment.checkedInAt && 
                                new Date(appointment.checkedInAt).toLocaleTimeString()}
                              </span>
                            )}
                          </div>
                          
                          <div className="flex space-x-2">
                            <Dialog>
                              <DialogTrigger asChild>
                                <Button 
                                  variant="outline" 
                                  size="sm"
                                  data-testid="button-view-patient"
                                >
                                  View Patient
                                </Button>
                              </DialogTrigger>
                              <DialogContent className="max-w-4xl">
                                <DialogHeader>
                                  <DialogTitle>Patient Information</DialogTitle>
                                </DialogHeader>
                                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                                  <div className="lg:col-span-2">
                                    <Tabs defaultValue="appointments" className="space-y-4">
                                      <TabsList>
                                        <TabsTrigger value="appointments">Appointments</TabsTrigger>
                                        <TabsTrigger value="records">Medical Records</TabsTrigger>
                                      </TabsList>
                                      
                                      <TabsContent value="appointments" className="space-y-4">
                                        <div className="text-center py-4">
                                          <p className="text-muted-foreground">
                                            Patient appointment history would be displayed here
                                          </p>
                                        </div>
                                      </TabsContent>
                                      
                                      <TabsContent value="records" className="space-y-4">
                                        <div className="text-center py-4">
                                          <p className="text-muted-foreground">
                                            Patient medical records would be displayed here
                                          </p>
                                        </div>
                                      </TabsContent>
                                    </Tabs>
                                  </div>
                                  
                                  <div className="space-y-4">
                                    <Card>
                                      <CardHeader>
                                        <CardTitle className="text-base">Patient Summary</CardTitle>
                                      </CardHeader>
                                      <CardContent className="space-y-3">
                                        <div className="text-center">
                                          <Avatar className="w-16 h-16 mx-auto mb-2">
                                            <AvatarFallback>
                                              <User className="h-8 w-8" />
                                            </AvatarFallback>
                                          </Avatar>
                                          <p className="font-medium">Patient ID: {appointment.patientId}</p>
                                        </div>
                                        
                                        <div className="space-y-2">
                                          <div className="flex items-center space-x-2">
                                            <Calendar className="h-4 w-4 text-muted-foreground" />
                                            <span className="text-sm">Current Appointment</span>
                                          </div>
                                          <div className="flex items-center space-x-2">
                                            <FileText className="h-4 w-4 text-muted-foreground" />
                                            <span className="text-sm">Medical Records Available</span>
                                          </div>
                                        </div>
                                      </CardContent>
                                    </Card>
                                  </div>
                                </div>
                              </DialogContent>
                            </Dialog>
                            
                            <Button variant="ghost" size="sm" data-testid="button-more-actions">
                              <MoreVertical className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Quick Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-6 text-center">
                <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-2">
                  <Users className="h-6 w-6 text-primary" />
                </div>
                <p className="text-sm text-muted-foreground">Total Today</p>
                <p className="text-2xl font-bold" data-testid="text-total-today">
                  {todayAppointments.length}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6 text-center">
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-2">
                  <Users className="h-6 w-6 text-green-600" />
                </div>
                <p className="text-sm text-muted-foreground">Checked In</p>
                <p className="text-2xl font-bold" data-testid="text-checked-in">
                  {todayAppointments.filter(apt => apt.checkedIn).length}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6 text-center">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-2">
                  <Clock className="h-6 w-6 text-blue-600" />
                </div>
                <p className="text-sm text-muted-foreground">Waiting</p>
                <p className="text-2xl font-bold" data-testid="text-waiting">
                  {todayAppointments.filter(apt => apt.checkedIn && apt.status === "scheduled").length}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6 text-center">
                <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-2">
                  <AlertTriangle className="h-6 w-6 text-yellow-600" />
                </div>
                <p className="text-sm text-muted-foreground">No Show</p>
                <p className="text-2xl font-bold" data-testid="text-no-show">
                  {todayAppointments.filter(apt => apt.status === "no_show").length}
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    </div>
  );
}
