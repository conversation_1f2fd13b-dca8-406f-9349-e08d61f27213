import { useState } from "react";
import { useAuth } from "@/hooks/use-auth";
import { useQuery } from "@tanstack/react-query";
import Sidebar from "@/components/layout/sidebar";
import Header from "@/components/layout/header";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { 
  BarChart3, 
  TrendingUp, 
  Users, 
  Calendar, 
  Clock, 
  Activity,
  Target,
  AlertCircle
} from "lucide-react";
import type { Appointment } from "@shared/schema";

export default function StaffAnalytics() {
  const { user } = useAuth();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [timeRange, setTimeRange] = useState("week");

  // Redirect non-staff users
  if (user?.role === "student") {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-foreground mb-2">Access Denied</h1>
          <p className="text-muted-foreground">Staff access required</p>
        </div>
      </div>
    );
  }

  const { data: todayAppointments = [] } = useQuery<Appointment[]>({
    queryKey: ["/api/appointments/date", new Date().toISOString().split('T')[0]],
  });

  // Calculate analytics data
  const totalAppointments = todayAppointments.length;
  const completedAppointments = todayAppointments.filter(apt => apt.status === "completed").length;
  const checkedInAppointments = todayAppointments.filter(apt => apt.checkedIn).length;
  const noShowAppointments = todayAppointments.filter(apt => apt.status === "no_show").length;
  const scheduledAppointments = todayAppointments.filter(apt => apt.status === "scheduled").length;

  const completionRate = totalAppointments > 0 ? Math.round((completedAppointments / totalAppointments) * 100) : 0;
  const checkInRate = totalAppointments > 0 ? Math.round((checkedInAppointments / totalAppointments) * 100) : 0;
  const noShowRate = totalAppointments > 0 ? Math.round((noShowAppointments / totalAppointments) * 100) : 0;

  // Appointment types distribution
  const appointmentTypes = todayAppointments.reduce((acc, apt) => {
    acc[apt.appointmentType] = (acc[apt.appointmentType] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const getAppointmentTypeLabel = (type: string) => {
    switch (type) {
      case "general":
        return "General";
      case "follow_up":
        return "Follow-up";
      case "vaccination":
        return "Vaccination";
      case "physical":
        return "Physical";
      case "mental_health":
        return "Mental Health";
      case "emergency":
        return "Emergency";
      default:
        return type;
    }
  };

  return (
    <div className="flex h-screen overflow-hidden bg-background">
      <Sidebar isOpen={sidebarOpen} onClose={() => setSidebarOpen(false)} />
      
      <main className="flex-1 overflow-auto">
        <Header 
          title="Analytics & Reports" 
          subtitle="Monitor clinic performance and patient statistics"
          onMenuClick={() => setSidebarOpen(true)}
          actions={
            <Select value={timeRange} onValueChange={setTimeRange}>
              <SelectTrigger className="w-32" data-testid="select-time-range">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="day">Today</SelectItem>
                <SelectItem value="week">This Week</SelectItem>
                <SelectItem value="month">This Month</SelectItem>
                <SelectItem value="year">This Year</SelectItem>
              </SelectContent>
            </Select>
          }
        />

        <div className="p-4 lg:p-6 space-y-6">
          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">Total Appointments</p>
                    <p className="text-2xl font-bold" data-testid="text-total-appointments">
                      {totalAppointments}
                    </p>
                    <p className="text-xs text-green-600 flex items-center">
                      <TrendingUp className="h-3 w-3 mr-1" />
                      +12% from last {timeRange}
                    </p>
                  </div>
                  <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
                    <Calendar className="h-6 w-6 text-primary" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">Completion Rate</p>
                    <p className="text-2xl font-bold" data-testid="text-completion-rate">
                      {completionRate}%
                    </p>
                    <p className="text-xs text-green-600 flex items-center">
                      <Target className="h-3 w-3 mr-1" />
                      Target: 85%
                    </p>
                  </div>
                  <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                    <Activity className="h-6 w-6 text-green-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">Check-in Rate</p>
                    <p className="text-2xl font-bold" data-testid="text-checkin-rate">
                      {checkInRate}%
                    </p>
                    <p className="text-xs text-blue-600 flex items-center">
                      <Users className="h-3 w-3 mr-1" />
                      {checkedInAppointments} checked in
                    </p>
                  </div>
                  <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                    <Users className="h-6 w-6 text-blue-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">No-Show Rate</p>
                    <p className="text-2xl font-bold" data-testid="text-noshow-rate">
                      {noShowRate}%
                    </p>
                    <p className="text-xs text-red-600 flex items-center">
                      <AlertCircle className="h-3 w-3 mr-1" />
                      {noShowAppointments} no-shows
                    </p>
                  </div>
                  <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
                    <AlertCircle className="h-6 w-6 text-red-600" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Charts and Detailed Analytics */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Appointment Status Distribution */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <BarChart3 className="h-5 w-5" />
                  <span>Appointment Status</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">Scheduled</span>
                    <div className="flex items-center space-x-2">
                      <div className="w-20 h-2 bg-muted rounded-full overflow-hidden">
                        <div 
                          className="h-full bg-blue-500 transition-all duration-300"
                          style={{ width: `${totalAppointments > 0 ? (scheduledAppointments / totalAppointments) * 100 : 0}%` }}
                        />
                      </div>
                      <span className="text-sm font-medium" data-testid="text-scheduled-count">
                        {scheduledAppointments}
                      </span>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">Completed</span>
                    <div className="flex items-center space-x-2">
                      <div className="w-20 h-2 bg-muted rounded-full overflow-hidden">
                        <div 
                          className="h-full bg-green-500 transition-all duration-300"
                          style={{ width: `${totalAppointments > 0 ? (completedAppointments / totalAppointments) * 100 : 0}%` }}
                        />
                      </div>
                      <span className="text-sm font-medium" data-testid="text-completed-count">
                        {completedAppointments}
                      </span>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">No Show</span>
                    <div className="flex items-center space-x-2">
                      <div className="w-20 h-2 bg-muted rounded-full overflow-hidden">
                        <div 
                          className="h-full bg-red-500 transition-all duration-300"
                          style={{ width: `${totalAppointments > 0 ? (noShowAppointments / totalAppointments) * 100 : 0}%` }}
                        />
                      </div>
                      <span className="text-sm font-medium" data-testid="text-noshow-count">
                        {noShowAppointments}
                      </span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Appointment Types */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Activity className="h-5 w-5" />
                  <span>Appointment Types</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {Object.entries(appointmentTypes).length === 0 ? (
                    <div className="text-center py-4">
                      <p className="text-muted-foreground">No appointment data available</p>
                    </div>
                  ) : (
                    Object.entries(appointmentTypes)
                      .sort(([,a], [,b]) => b - a)
                      .map(([type, count]) => (
                        <div key={type} className="flex items-center justify-between">
                          <span className="text-sm text-muted-foreground">
                            {getAppointmentTypeLabel(type)}
                          </span>
                          <div className="flex items-center space-x-2">
                            <div className="w-20 h-2 bg-muted rounded-full overflow-hidden">
                              <div 
                                className="h-full bg-primary transition-all duration-300"
                                style={{ width: `${totalAppointments > 0 ? (count / totalAppointments) * 100 : 0}%` }}
                              />
                            </div>
                            <span className="text-sm font-medium" data-testid={`text-type-${type}-count`}>
                              {count}
                            </span>
                          </div>
                        </div>
                      ))
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Performance Insights */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <TrendingUp className="h-5 w-5" />
                <span>Performance Insights</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center space-y-2">
                  <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                    <Target className="h-8 w-8 text-green-600" />
                  </div>
                  <h3 className="font-medium">Efficiency</h3>
                  <p className="text-2xl font-bold text-green-600" data-testid="text-efficiency-score">
                    {completionRate}%
                  </p>
                  <p className="text-sm text-muted-foreground">
                    Appointment completion rate
                  </p>
                </div>

                <div className="text-center space-y-2">
                  <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto">
                    <Clock className="h-8 w-8 text-blue-600" />
                  </div>
                  <h3 className="font-medium">Punctuality</h3>
                  <p className="text-2xl font-bold text-blue-600" data-testid="text-punctuality-score">
                    {100 - noShowRate}%
                  </p>
                  <p className="text-sm text-muted-foreground">
                    Patient attendance rate
                  </p>
                </div>

                <div className="text-center space-y-2">
                  <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto">
                    <Activity className="h-8 w-8 text-primary" />
                  </div>
                  <h3 className="font-medium">Utilization</h3>
                  <p className="text-2xl font-bold text-primary" data-testid="text-utilization-score">
                    {Math.round((totalAppointments / 20) * 100)}%
                  </p>
                  <p className="text-sm text-muted-foreground">
                    Clinic capacity utilization
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Recommendations */}
          <Card>
            <CardHeader>
              <CardTitle>Recommendations</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {noShowRate > 15 && (
                  <div className="flex items-start space-x-3 p-4 bg-red-50 rounded-lg">
                    <AlertCircle className="h-5 w-5 text-red-600 mt-0.5" />
                    <div>
                      <h4 className="font-medium text-red-800">High No-Show Rate</h4>
                      <p className="text-sm text-red-600">
                        Consider implementing reminder calls or SMS notifications to reduce no-shows.
                      </p>
                    </div>
                  </div>
                )}

                {completionRate < 80 && (
                  <div className="flex items-start space-x-3 p-4 bg-yellow-50 rounded-lg">
                    <Target className="h-5 w-5 text-yellow-600 mt-0.5" />
                    <div>
                      <h4 className="font-medium text-yellow-800">Low Completion Rate</h4>
                      <p className="text-sm text-yellow-600">
                        Review appointment scheduling and workflow to improve completion rates.
                      </p>
                    </div>
                  </div>
                )}

                {totalAppointments > 0 && completionRate >= 90 && noShowRate < 10 && (
                  <div className="flex items-start space-x-3 p-4 bg-green-50 rounded-lg">
                    <TrendingUp className="h-5 w-5 text-green-600 mt-0.5" />
                    <div>
                      <h4 className="font-medium text-green-800">Excellent Performance</h4>
                      <p className="text-sm text-green-600">
                        Your clinic is performing exceptionally well with high completion rates and low no-shows.
                      </p>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
}
